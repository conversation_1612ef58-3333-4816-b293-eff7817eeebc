"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const app_1 = __importDefault(require("./app"));
const db_1 = __importDefault(require("./db"));
// Load environment variables
dotenv_1.default.config();
const PORT = parseInt(process.env.PORT || "5000", 10);
const NODE_ENV = process.env.NODE_ENV || "development";
/**
 * Start the server
 */
const startServer = async () => {
    try {
        // Connect to database first
        await (0, db_1.default)();
        // Start the server
        const server = app_1.default.listen(PORT, () => {
            console.log(`🚀 Server running on port ${PORT}`);
            console.log(`🌍 Environment: ${NODE_ENV}`);
            console.log(`📅 Started at: ${new Date().toISOString()}`);
            if (NODE_ENV === 'development') {
                console.log(`🔗 Local URL: http://localhost:${PORT}`);
                console.log(`🏥 Health check: http://localhost:${PORT}/health`);
            }
        });
        // Handle unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
            process.exit(1);
        });
    }
    catch (error) {
        console.error("💥 Failed to start server:", error);
        if (error instanceof Error) {
            console.error("Error message:", error.message);
            console.error("Error stack:", error.stack);
        }
        process.exit(1);
    }
};
// Start the server
startServer();

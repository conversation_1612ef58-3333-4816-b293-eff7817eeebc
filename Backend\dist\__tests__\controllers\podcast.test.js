"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const supertest_1 = __importDefault(require("supertest"));
const app_1 = __importDefault(require("../../app"));
const models_1 = require("../../models");
const mock_1 = require("../../utils/mock");
(0, vitest_1.beforeEach)(() => {
    vitest_1.vi.restoreAllMocks();
});
(0, vitest_1.describe)("Podcast API Endpoints", () => {
    // ---------------- GET /podcasts ----------------
    (0, vitest_1.describe)("GET /podcasts", () => {
        (0, vitest_1.it)("should return paginated list of podcasts", async () => {
            const paginateMock = {
                totalDocs: mock_1.mockPodcasts.length,
                totalPages: 1,
                page: 1,
                docs: mock_1.mockPodcasts,
            };
            vitest_1.vi.spyOn(models_1.Podcast, "paginate").mockResolvedValue(paginateMock);
            const res = await (0, supertest_1.default)(app_1.default).get("/api/v1/podcasts?page=1&limit=10");
            (0, vitest_1.expect)(res.status).toBe(200);
            (0, vitest_1.expect)(res.body.totalPodcasts).toBe(mock_1.mockPodcasts.length);
            (0, vitest_1.expect)(res.body.podcasts[0].title).toBe(mock_1.mockPodcasts[0].title);
        });
    });
    // ---------------- GET /podcasts/:id ----------------
    (0, vitest_1.describe)("GET /podcasts/:id", () => {
        (0, vitest_1.it)("should return a single podcast with episodes", async () => {
            const podcastWithEpisodes = { ...mock_1.mockPodcasts[0], episodes: mock_1.mockEpisodesPodcast1 };
            vitest_1.vi.spyOn(models_1.Podcast, "findById").mockReturnValue({
                populate: vitest_1.vi.fn().mockResolvedValue(podcastWithEpisodes)
            });
            const res = await (0, supertest_1.default)(app_1.default).get(`/api/v1/podcasts/${mock_1.mockPodcasts[0]._id}`);
            (0, vitest_1.expect)(res.status).toBe(200);
            (0, vitest_1.expect)(res.body.title).toBe(mock_1.mockPodcasts[0].title);
            (0, vitest_1.expect)(res.body.episodes.length).toBe(mock_1.mockEpisodesPodcast1.length);
        });
        (0, vitest_1.it)("should return 404 if podcast not found", async () => {
            vitest_1.vi.spyOn(models_1.Podcast, "findById").mockReturnValue({
                populate: vitest_1.vi.fn().mockResolvedValue(null)
            });
            const res = await (0, supertest_1.default)(app_1.default).get(`/api/v1/podcasts/507f1f77bcf86cd799439011`);
            (0, vitest_1.expect)(res.status).toBe(404);
            (0, vitest_1.expect)(res.body.message).toBe("Podcast not found");
        });
    });
    // ---------------- GET /podcasts/search ----------------
    (0, vitest_1.describe)("GET /podcasts/search", () => {
        (0, vitest_1.it)("should search podcasts by query", async () => {
            const paginateMock = {
                totalDocs: 1,
                totalPages: 1,
                page: 1,
                docs: [mock_1.mockPodcasts[0]],
            };
            vitest_1.vi.spyOn(models_1.Podcast, "paginate").mockResolvedValue(paginateMock);
            const res = await (0, supertest_1.default)(app_1.default).get("/api/v1/podcasts/search/filter?query=Tech&page=1&limit=10");
            (0, vitest_1.expect)(res.status).toBe(200);
            (0, vitest_1.expect)(res.body.totalResults).toBe(1);
            (0, vitest_1.expect)(res.body.podcasts[0].title).toContain("Tech");
        });
        (0, vitest_1.it)("should filter podcasts by category", async () => {
            const paginateMock = {
                totalDocs: 1,
                totalPages: 1,
                page: 1,
                docs: [mock_1.mockPodcasts[0]],
            };
            vitest_1.vi.spyOn(models_1.Podcast, "paginate").mockResolvedValue(paginateMock);
            const res = await (0, supertest_1.default)(app_1.default).get("/api/v1/podcasts/search/filter?category=Technology");
            (0, vitest_1.expect)(res.status).toBe(200);
            (0, vitest_1.expect)(res.body.totalResults).toBe(1);
            (0, vitest_1.expect)(res.body.podcasts[0].category).toBe("Technology");
        });
        (0, vitest_1.it)("should return empty array if no results", async () => {
            const paginateMock = {
                totalDocs: 0,
                totalPages: 0,
                page: 1,
                docs: [],
            };
            vitest_1.vi.spyOn(models_1.Podcast, "paginate").mockResolvedValue(paginateMock);
            const res = await (0, supertest_1.default)(app_1.default).get("/api/v1/podcasts/search/filter?query=NonExistent");
            (0, vitest_1.expect)(res.status).toBe(200);
            (0, vitest_1.expect)(res.body.totalResults).toBe(0);
            (0, vitest_1.expect)(res.body.podcasts).toEqual([]);
        });
    });
});

/**
 * RIPEMD-160 legacy hash function.
 * https://homes.esat.kuleuven.be/~bosselae/ripemd160.html
 * https://homes.esat.kuleuven.be/~bosselae/ripemd160/pdf/AB-9601/AB-9601.pdf
 * @module
 * @deprecated
 */
import { RIPEMD160 as RIPEMD160n, ripemd160 as ripemd160n } from "./legacy.js";
/** @deprecated Use import from `noble/hashes/legacy` module */
export const RIPEMD160 = RIPEMD160n;
/** @deprecated Use import from `noble/hashes/legacy` module */
export const ripemd160 = ripemd160n;
//# sourceMappingURL=ripemd160.js.map
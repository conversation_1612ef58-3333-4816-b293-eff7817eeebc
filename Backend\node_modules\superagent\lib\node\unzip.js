"use strict";

/**
 * Module dependencies.
 */

const {
  StringDecoder
} = require('string_decoder');
const Stream = require('stream');
const {
  chooseDecompresser
} = require('./decompress');

/**
 * Buffers response data events and re-emits when they're decompressed.
 *
 * @param {Request} req
 * @param {Response} res
 * @api private
 */

exports.decompress = (request, res) => {
  let decompresser = chooseDecompresser(res);
  const stream = new Stream();
  let decoder;

  // make node responseOnEnd() happy
  stream.req = request;
  decompresser.on('error', error => {
    if (error && error.code === 'Z_BUF_ERROR') {
      // unexpected end of file is ignored by browsers and curl
      stream.emit('end');
      return;
    }
    stream.emit('error', error);
  });

  // pipe to unzip
  res.pipe(decompresser);

  // override `setEncoding` to capture encoding
  res.setEncoding = type => {
    decoder = new StringDecoder(type);
  };

  // decode upon decompressing with captured encoding
  decompresser.on('data', buf => {
    if (decoder) {
      const string_ = decoder.write(buf);
      if (string_.length > 0) stream.emit('data', string_);
    } else {
      stream.emit('data', buf);
    }
  });
  decompresser.on('end', () => {
    stream.emit('end');
  });

  // override `on` to capture data listeners
  const _on = res.on;
  res.on = function (type, fn) {
    if (type === 'data' || type === 'end') {
      stream.on(type, fn.bind(res));
    } else if (type === 'error') {
      stream.on(type, fn.bind(res));
      _on.call(res, type, fn);
    } else {
      _on.call(res, type, fn);
    }
    return this;
  };
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
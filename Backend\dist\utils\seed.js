"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const dotenv_1 = __importDefault(require("dotenv"));
const faker_1 = require("@faker-js/faker");
const index_js_1 = require("../models/index.js");
const constants_1 = require("./constants");
dotenv_1.default.config();
const MONGO_URI = process.env.MONGO_URI;
async function seed() {
    try {
        if (!MONGO_URI) {
            throw new Error("MONGODB_URI environment variable is not defined");
        }
        await mongoose_1.default.connect(MONGO_URI);
        console.log("✅ Connected to MongoDB for seeding");
        // Clear old data
        await Promise.all([
            index_js_1.User.deleteMany({}),
            index_js_1.Podcast.deleteMany({}),
            index_js_1.Episode.deleteMany({}),
        ]);
        console.log("🧹 Old data cleared");
        // Create users
        const usersData = [
            {
                name: "Alice Johnson",
                email: "<EMAIL>",
                password: "hashed1234",
                avatarUrl: faker_1.faker.image.avatar(),
                role: constants_1.UserRole.USER,
            },
            {
                name: "Bob Smith",
                email: "<EMAIL>",
                password: "hashed1234",
                avatarUrl: faker_1.faker.image.avatar(),
                role: constants_1.UserRole.ADMIN,
            },
        ];
        const users = await index_js_1.User.insertMany(usersData);
        console.log(`Created ${users.length} users`);
        // Create podcasts
        const podcastsData = [
            {
                title: "Tech Minds",
                author: "Ethan Ray",
                description: faker_1.faker.lorem.paragraph(),
                category: constants_1.PodcastCategory.TECHNOLOGY,
                coverImageUrl: faker_1.faker.image.urlLoremFlickr({ category: "technology" }),
            },
            {
                title: "Health Matters",
                author: "Dr. Jane Lee",
                description: faker_1.faker.lorem.paragraph(),
                category: constants_1.PodcastCategory.HEALTH,
                coverImageUrl: faker_1.faker.image.urlLoremFlickr({ category: "health" }),
            },
            {
                title: "Business Unplugged",
                author: "Mark Thomas",
                description: faker_1.faker.lorem.paragraph(),
                category: constants_1.PodcastCategory.BUSINESS,
                coverImageUrl: faker_1.faker.image.urlLoremFlickr({ category: "business" }),
            },
        ];
        const podcasts = await index_js_1.Podcast.insertMany(podcastsData);
        console.log(`Created ${podcasts.length} podcasts`);
        // Create episodes
        const episodeCounts = [3, 10, 15];
        const allEpisodes = [];
        for (let i = 0; i < podcasts.length; i++) {
            const podcast = podcasts[i];
            const count = episodeCounts[i] || 0;
            if (!podcast) {
                console.warn(`⚠️ Podcast at index ${i} is undefined, skipping...`);
                continue;
            }
            const episodesData = Array.from({ length: count }).map(() => ({
                title: faker_1.faker.lorem.words(4),
                description: faker_1.faker.lorem.paragraph(),
                audioUrl: `https://cdn.podcastapp.com/${faker_1.faker.word.noun()}.mp3`,
                duration: faker_1.faker.number.int({ min: 60, max: 3600 }),
                podcast: podcast._id,
                publishedAt: faker_1.faker.date.past(),
            }));
            const episodes = await index_js_1.Episode.insertMany(episodesData);
            // Update podcast with episode references
            podcast.episodes = episodes.map((e) => e._id);
            if (podcast.save) {
                await podcast.save();
            }
            allEpisodes.push(...episodes);
            console.log(`🎧 Added ${count} episodes to podcast: ${podcast.title}`);
        }
        // Assign favorites & recently played to users
        for (const user of users) {
            if (!user) {
                console.warn(`User is undefined, skipping...`);
                continue;
            }
            const favCount = faker_1.faker.number.int({ min: 1, max: podcasts.length });
            const favorites = faker_1.faker.helpers.arrayElements(podcasts, favCount).map((p) => p._id);
            const playedCount = faker_1.faker.number.int({ min: 1, max: 10 });
            const recentlyPlayed = faker_1.faker.helpers.arrayElements(allEpisodes, playedCount).map((e) => e._id);
            user.favorites = favorites;
            user.recentlyPlayed = recentlyPlayed;
            if (user.save) {
                await user.save();
            }
        }
        console.log("Favorites & recently played assigned");
        console.log("🌱 Seeding complete!");
        process.exit(0);
    }
    catch (err) {
        console.error("❌ Error seeding:", err);
        process.exit(1);
    }
}
seed();

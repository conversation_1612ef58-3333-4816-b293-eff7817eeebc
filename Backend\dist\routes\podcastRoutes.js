"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const podcast_1 = __importDefault(require("../controllers/podcast"));
const express_1 = require("express");
const { getAllPodcasts, getPodcastById, podcastSearchAndFilterByTitleOrCategory } = podcast_1.default;
const podcastRouter = (0, express_1.Router)();
podcastRouter.get("/", getAllPodcasts);
podcastRouter.get("/search/filter", podcastSearchAndFilterByTitleOrCategory);
podcastRouter.get("/:id", getPodcastById);
exports.default = podcastRouter;

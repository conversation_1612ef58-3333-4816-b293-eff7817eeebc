"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Episode = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const mongoose_paginate_v2_1 = __importDefault(require("mongoose-paginate-v2"));
// Episode Schema
const episodeSchema = new mongoose_1.Schema({
    title: {
        type: String,
        required: [true, "Title is required"],
        trim: true,
        minlength: [3, "Title must be at least 3 characters long"],
        maxlength: [300, "Title cannot exceed 300 characters"],
    },
    description: {
        type: String,
        trim: true,
        maxlength: [2000, "Description cannot exceed 2000 characters"],
    },
    audioUrl: {
        type: String, // S3 bucket URL or other audio hosting service
        required: [true, "Audio URL is required"],
        validate: {
            validator: function (v) {
                return /^https?:\/\/.+\.(mp3|wav|m4a|aac|ogg|flac)$/i.test(v) ||
                    /^https?:\/\/.+/.test(v); // Allow any HTTPS URL for flexibility
            },
            message: "Audio URL must be a valid audio file URL",
        },
    },
    duration: {
        type: Number, // in seconds,
        default: 0,
        min: [0, "Duration cannot be negative"],
        max: [86400, "Duration cannot exceed 24 hours"], // 24 hours in seconds
    },
    podcast: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: "Podcast",
        required: [true, "Podcast reference is required"],
    },
    publishedAt: {
        type: Date,
        default: Date.now,
    },
    playCount: {
        type: Number,
        default: 0,
        min: [0, "Play count cannot be negative"],
    },
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Virtual for formatted duration
episodeSchema.virtual('formattedDuration').get(function () {
    if (!this.duration)
        return null;
    const hours = Math.floor(this.duration / 3600);
    const minutes = Math.floor((this.duration % 3600) / 60);
    const seconds = this.duration % 60;
    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
});
// Virtual for relative published date
episodeSchema.virtual('publishedAgo').get(function () {
    const now = new Date();
    const diffMs = now.getTime() - this.publishedAt.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    if (diffDays === 0)
        return 'Today';
    if (diffDays === 1)
        return 'Yesterday';
    if (diffDays < 7)
        return `${diffDays} days ago`;
    if (diffDays < 30)
        return `${Math.floor(diffDays / 7)} weeks ago`;
    if (diffDays < 365)
        return `${Math.floor(diffDays / 30)} months ago`;
    return `${Math.floor(diffDays / 365)} years ago`;
});
// Indexes for better performance
episodeSchema.index({ podcast: 1, publishedAt: -1 });
episodeSchema.index({ title: 'text', description: 'text' });
episodeSchema.index({ publishedAt: -1 });
episodeSchema.index({ playCount: -1 });
// Add pagination plugin
episodeSchema.plugin(mongoose_paginate_v2_1.default);
exports.Episode = mongoose_1.default.model("Episode", episodeSchema);

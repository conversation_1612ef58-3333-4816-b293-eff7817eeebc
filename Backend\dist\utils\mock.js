"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mockEpisodesPodcast3 = exports.mockEpisodesPodcast2 = exports.mockEpisodesPodcast1 = exports.mockPodcasts = exports.mockUsers = void 0;
const mongoose_1 = require("mongoose");
const constants_1 = require("../utils/constants");
// -------------------- USERS --------------------
exports.mockUsers = [
    {
        _id: new mongoose_1.Types.ObjectId(),
        name: "<PERSON>",
        email: "<EMAIL>",
        password: "hashedpassword1",
        favorites: [],
        recentlyPlayed: [],
        avatarUrl: "https://example.com/avatar1.jpg",
        role: constants_1.UserRole.USER,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        _id: new mongoose_1.Types.ObjectId(),
        name: "<PERSON>",
        email: "<EMAIL>",
        password: "hashedpassword2",
        favorites: [],
        recentlyPlayed: [],
        avatarUrl: "https://example.com/avatar2.jpg",
        role: constants_1.UserRole.ADMIN,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
];
// -------------------- PODCASTS --------------------
exports.mockPodcasts = [
    {
        _id: new mongoose_1.Types.ObjectId(),
        title: "Tech Talks Daily",
        description: "Your daily dose of tech insights and innovations.",
        author: "Alice Johnson",
        category: constants_1.PodcastCategory.TECHNOLOGY,
        coverImageUrl: "https://example.com/cover-tech.jpg",
        episodes: [],
        followers: [exports.mockUsers[0]._id, exports.mockUsers[1]._id],
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        _id: new mongoose_1.Types.ObjectId(),
        title: "The Startup Mindset",
        description: "Stories and lessons from successful entrepreneurs.",
        author: "Bob Smith",
        category: constants_1.PodcastCategory.BUSINESS,
        coverImageUrl: "https://example.com/cover-startup.jpg",
        episodes: [],
        followers: [exports.mockUsers[0]._id],
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        _id: new mongoose_1.Types.ObjectId(),
        title: "Science Simplified",
        description: "Explaining complex scientific concepts in simple terms.",
        author: "Alice Johnson",
        category: constants_1.PodcastCategory.TECHNOLOGY,
        coverImageUrl: "https://example.com/cover-science.jpg",
        episodes: [],
        followers: [exports.mockUsers[1]._id],
        createdAt: new Date(),
        updatedAt: new Date(),
    },
];
// -------------------- EPISODES --------------------
// Helper function to create mock episodes
function createMockEpisodes(count, podcastId) {
    return Array.from({ length: count }).map((_, i) => ({
        _id: new mongoose_1.Types.ObjectId(),
        title: `Episode ${i + 1}`,
        description: `Description for episode ${i + 1}`,
        audioUrl: `https://example.com/audio/${podcastId}/${i + 1}.mp3`,
        duration: 180 + i * 10,
        podcast: podcastId,
        publishedAt: new Date(),
        playCount: Math.floor(Math.random() * 1000),
        createdAt: new Date(),
        updatedAt: new Date(),
    }));
}
exports.mockEpisodesPodcast1 = createMockEpisodes(3, exports.mockPodcasts[0]._id);
exports.mockEpisodesPodcast2 = createMockEpisodes(10, exports.mockPodcasts[1]._id);
exports.mockEpisodesPodcast3 = createMockEpisodes(15, exports.mockPodcasts[2]._id);
// Link episodes to their podcasts
exports.mockPodcasts[0].episodes = exports.mockEpisodesPodcast1.map(e => e._id);
exports.mockPodcasts[1].episodes = exports.mockEpisodesPodcast2.map(e => e._id);
exports.mockPodcasts[2].episodes = exports.mockEpisodesPodcast3.map(e => e._id);

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const podcast_1 = require("../models/podcast");
/**
 * @desc Get all podcasts (paginated)
 * @route GET /podcasts
 * @access Public
 */
const getAllPodcasts = async (req, res) => {
    try {
        const { page = "1", limit = "10" } = req.query;
        const options = {
            page: parseInt(page, 10),
            limit: parseInt(limit, 10),
            sort: { createdAt: -1 },
            select: "title author description category coverImageUrl createdAt",
        };
        const podcasts = await podcast_1.Podcast.paginate({}, options);
        res.status(200).json({
            totalPodcasts: podcasts.totalDocs,
            totalPages: podcasts.totalPages,
            currentPage: podcasts.page,
            podcasts: podcasts.docs,
        });
    }
    catch (error) {
        console.error("Error fetching podcasts:", error);
        res.status(500).json({ message: "Server error" });
    }
};
/**
 * @desc Get a single podcast by ID (with its episodes)
 * @route GET /podcasts/:id
 * @access Public
 */
const getPodcastById = async (req, res) => {
    try {
        const { id } = req.params;
        const podcast = await podcast_1.Podcast.findById(id).populate({
            path: "episodes",
            select: "title description audioUrl duration createdAt",
            options: { sort: { createdAt: -1 } },
        });
        if (!podcast) {
            res.status(404).json({ message: "Podcast not found" });
            return;
        }
        res.status(200).json(podcast);
    }
    catch (error) {
        console.error("Error fetching podcast by ID:", error);
        res.status(500).json({ message: "Server error" });
    }
};
/**
 * @desc Search podcasts by title or filter by category (paginated)
 * @route GET /podcasts/search?query=react&category=Technology&page=1&limit=5
 * @access Public
 */
const podcastSearchAndFilterByTitleOrCategory = async (req, res) => {
    try {
        const { query = "", category, page = "1", limit = "10" } = req.query;
        const filter = {};
        if (query) {
            filter.title = { $regex: query, $options: "i" };
        }
        if (category && category !== "All") {
            filter.category = category;
        }
        const options = {
            page: parseInt(page, 10),
            limit: parseInt(limit, 10),
            sort: { createdAt: -1 },
            select: "title author description category coverImageUrl createdAt",
        };
        const podcasts = await podcast_1.Podcast.paginate(filter, options);
        res.status(200).json({
            totalResults: podcasts.totalDocs,
            totalPages: podcasts.totalPages,
            currentPage: podcasts.page,
            podcasts: podcasts.docs,
        });
    }
    catch (error) {
        console.error("Error searching/filtering podcasts:", error);
        res.status(500).json({ message: "Server error" });
    }
};
exports.default = {
    getAllPodcasts,
    getPodcastById,
    podcastSearchAndFilterByTitleOrCategory,
};
